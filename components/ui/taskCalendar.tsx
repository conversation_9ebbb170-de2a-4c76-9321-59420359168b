"use client"

import * as React from "react"
import { Calendar } from "@/components/ui/calendar"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CalendarDays, Clock, MapPin } from "lucide-react"
import { formatDate, formatTime, Task } from "@/app/api/parse-event/route"
import { cn } from "@/lib/utils"

interface TaskCalendarProps {
  tasks: Task[]
  onDateSelect?: (date: Date | undefined) => void
  selectedDate?: Date
}

export function TaskCalendar({ tasks, onDateSelect, selectedDate }: TaskCalendarProps) {
  const [date, setDate] = React.useState<Date | undefined>(selectedDate || new Date())

  // Group tasks by date for easy lookup
  const tasksByDate = React.useMemo(() => {
    const grouped: { [key: string]: Task[] } = {}
    tasks.forEach(task => {
      const dateKey = new Date(task.date).toDateString()
      if (!grouped[dateKey]) {
        grouped[dateKey] = []
      }
      grouped[dateKey].push(task)
    })
    return grouped
  }, [tasks])

  // Get tasks for selected date
  const selectedDateTasks = React.useMemo(() => {
    if (!date) return []
    const dateKey = date.toDateString()
    return tasksByDate[dateKey] || []
  }, [date, tasksByDate])

  const handleDateSelect = (newDate: Date | undefined) => {
    setDate(newDate)
    onDateSelect?.(newDate)
  }

  const getCategoryColor = (category: string) => {
    switch (category?.toLowerCase()) {
      case 'meeting': return 'bg-blue-600 border-l-blue-600'
      case 'exercise': case 'fitness': return 'bg-green-600 border-l-green-600'
      case 'personal': return 'bg-purple-600 border-l-purple-600'
      case 'call': return 'bg-orange-600 border-l-orange-600'
      case 'appointment': return 'bg-pink-600 border-l-pink-600'
      case 'work': return 'bg-purple-600 border-l-purple-600'
      case 'study': return 'bg-cyan-600 border-l-cyan-600'
      default: return 'bg-yellow-600 border-l-yellow-600'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-green-100 text-green-800 border-green-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Calendar */}
      <div className="lg:col-span-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CalendarDays className="h-5 w-5" />
              Task Calendar
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="w-full">
              <Calendar
                mode="single"
                selected={date}
                onSelect={handleDateSelect}
                className="w-full rounded-lg border-0 [--cell-size:120px]"
                classNames={{
                  day: cn(
                    "relative w-full h-[120px] p-1 text-left align-top border border-gray-200 hover:bg-gray-50",
                    "data-[selected=true]:bg-blue-50 data-[selected=true]:border-blue-300"
                  ),
                  table: "w-full border-collapse border border-gray-300",
                  head_cell: "border border-gray-300 p-2 bg-gray-50 font-medium text-center",
                  cell: "border border-gray-300 p-0 relative",
                }}
                components={{
                    DayButton: ({ day, ...props }: { day: { date: Date }, [key: string]: any }) => {
                    const dayTasks = tasksByDate[dayDate.toDateString()] || []
                    const dayNumber = dayDate.getDate()
                    const isToday = dayDate.toDateString() === new Date().toDateString()
                    
                    return (
                      <div className="w-full h-full flex flex-col">
                        {/* Day number */}
                        <div className={cn(
                          "text-sm font-medium mb-1 flex-shrink-0",
                          isToday ? "bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center" : ""
                        )}>
                          {dayNumber}
                        </div>
                        
                        {/* Tasks */}
                        <div className="flex-1 space-y-0.5 overflow-hidden">
                          {dayTasks.slice(0, 6).map((task, index) => (
                            <div
                              key={index}
                              className={cn(
                                "text-xs px-1 py-0.5 rounded text-white truncate border-l-2",
                                getCategoryColor(task.category ?? '')
                              )}
                              title={`${task.title} - ${formatTime(task.startTime)}${task.location ? ` at ${task.location}` : ''}`}
                            >
                              {formatTime(task.startTime).replace(' ', '')} {task.title}
                            </div>
                          ))}
                          
                          {dayTasks.length > 6 && (
                            <div className="text-xs text-gray-500 px-1">
                              +{dayTasks.length - 6} more
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  }
                }}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Task Details for Selected Date - shown below calendar on smaller screens */}
      {selectedDateTasks.length > 0 && (
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center justify-between">
                <span>{date ? formatDate(date.toISOString()) : 'Selected Date'}</span>
                <Badge variant="secondary">
                  {selectedDateTasks.length} task{selectedDateTasks.length !== 1 ? 's' : ''}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {selectedDateTasks
                  .sort((a, b) => a.startTime.localeCompare(b.startTime))
                  .map((task, index) => (
                    <div key={index} className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex items-start justify-between gap-2 mb-2">
                        <h4 className="font-medium text-sm leading-tight">{task.title}</h4>
                        {task.priority && (
                          <Badge variant="outline" className={`text-xs ${getPriorityColor(task.priority)}`}>
                            {task.priority}
                          </Badge>
                        )}
                      </div>
                      
                      {task.category && (
                        <div className={cn(
                          "inline-block px-2 py-1 rounded text-xs text-white mb-2",
                          getCategoryColor(task.category).split(' ')[0]
                        )}>
                          {task.category}
                        </div>
                      )}

                      <div className="space-y-1 text-xs text-gray-600">
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>
                            {formatTime(task.startTime)}
                            {task.endTime && ` - ${formatTime(task.endTime)}`}
                            {task.duration && !task.endTime && ` (${task.duration})`}
                          </span>
                        </div>

                        {task.location && (
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            <span>{task.location}</span>
                          </div>
                        )}

                        {task.description && (
                          <p className="text-gray-500 mt-2">{task.description}</p>
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}